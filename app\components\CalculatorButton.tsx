'use client';

import React, { useCallback } from 'react';
import { ButtonConfig } from '../types/calculator';

interface CalculatorButtonProps {
  config: ButtonConfig;
  onClick: (value: string, type: ButtonConfig['type']) => void;
  disabled?: boolean;
  className?: string;
}

export const CalculatorButton: React.FC<CalculatorButtonProps> = ({
  config,
  onClick,
  disabled = false,
  className = '',
}) => {
  const handleClick = useCallback(() => {
    if (disabled) return;
    
    // Vibration feedback for mobile devices
    if ('vibrate' in navigator) {
      navigator.vibrate(50);
    }
    
    onClick(config.value, config.type);
  }, [config.value, config.type, onClick, disabled]);

  const getButtonStyles = (): string => {
    // Simplified styling per user's request
    const baseStyles = `
      relative overflow-hidden rounded-lg text-white py-4 text-lg
      transition-colors duration-150
      disabled:opacity-50 disabled:cursor-not-allowed
      select-none touch-manipulation
    `;

    const typeStyles = {
      // Default gray keys
      number: `bg-gray-800 hover:bg-gray-700`,
      function: `bg-gray-800 hover:bg-gray-700`,
      memory: `bg-gray-800 hover:bg-gray-700`,
      special: `bg-gray-800 hover:bg-gray-700`,
      // Operators in orange
      operator: `bg-orange-500 hover:bg-orange-600`,
    } as const;

    return `${baseStyles} ${typeStyles[config.type]} ${config.className || ''} ${className}`;
  };

  const getGridStyles = (): React.CSSProperties => {
    const styles: React.CSSProperties = {};
    
    if (config.colspan && config.colspan > 1) {
      styles.gridColumn = `span ${config.colspan}`;
    }
    
    if (config.rowspan && config.rowspan > 1) {
      styles.gridRow = `span ${config.rowspan}`;
    }
    
    return styles;
  };

  return (
    <button
      className={getButtonStyles()}
      style={getGridStyles()}
      onClick={handleClick}
      disabled={disabled}
      type="button"
      aria-label={`${config.type} ${config.label}`}
    >
      {/* Ripple effect overlay */}
      <span className="absolute inset-0 bg-white opacity-0 transition-opacity duration-150 active:opacity-20" />
      
      {/* Button content */}
      <span className="relative z-10 flex items-center justify-center h-full">
        {config.label}
      </span>
    </button>
  );
};
