'use client';

import React, { useCallback, useEffect, useMemo, useState } from 'react';
import styles from './ScientificCalculator.module.css';
import { evaluate, format, factorial as mfactorial } from 'mathjs';

type Key = {
  label: string;
  value: string;
  aria?: string;
  cls?: string;
};

function factorial(n: number): number {
  if (n < 0) throw new Error('Factorial of negative');
  if (n === 0) return 1;
  return n * factorial(n - 1);
}

export default function ScientificCalculator() {
  const [expr, setExpr] = useState('');
  const [value, setValue] = useState('0');
  const [error, setError] = useState<string | null>(null);

  const keys: Key[] = [
    { label: 'AC', value: 'AC', cls: styles.btnDanger, aria: 'All Clear' },
    { label: 'C', value: 'C', cls: styles.btnDanger, aria: 'Clear' },
    { label: '(', value: '(' },
    { label: ')', value: ')' },
    { label: '÷', value: '/' , cls: styles.btnOp},

    { label: 'sin', value: 'sin(' },
    { label: 'cos', value: 'cos(' },
    { label: 'tan', value: 'tan(' },
    { label: 'ln', value: 'ln(' },
    { label: 'log', value: 'log10(' },

    { label: '7', value: '7' },
    { label: '8', value: '8' },
    { label: '9', value: '9' },
    { label: '×', value: '*', cls: styles.btnOp },
    { label: '^', value: '^', cls: styles.btnOp },

    { label: '4', value: '4' },
    { label: '5', value: '5' },
    { label: '6', value: '6' },
    { label: '−', value: '-', cls: styles.btnOp },
    { label: '√', value: 'sqrt(' },

    { label: '1', value: '1' },
    { label: '2', value: '2' },
    { label: '3', value: '3' },
    { label: '+', value: '+', cls: styles.btnOp },
    { label: 'x!', value: 'factorial', cls: styles.btnOp },

    { label: '±', value: 'neg' },
    { label: '0', value: '0' },
    { label: '.', value: '.' },
    { label: '=', value: '=', cls: styles.btnAccent },
    { label: 'Ans', value: 'ans', cls: styles.btnOp },
  ];

  // Instant evaluation where safe
  useEffect(() => {
    setError(null);
    if (!expr) { setValue('0'); return; }
    try {
      const sanitized = expr.replace(/×/g, '*').replace(/÷/g, '/').replace(/−/g, '-');
      const result = evaluate(sanitized);
      if (typeof result === 'number') {
        const formatted = format(result, { precision: 14 });
        setValue(formatted);
      } else {
        setValue(String(result));
      }
    } catch {
      // ignore until expression complete
    }
  }, [expr]);

  const onKey = useCallback((k: Key) => {
    setError(null);
    if (k.value === 'AC') { setExpr(''); setValue('0'); return; }
    if (k.value === 'C') { setExpr(prev => prev.slice(0, -1)); return; }
    if (k.value === '=') {
      try {
        const sanitized = expr.replace(/×/g, '*').replace(/÷/g, '/').replace(/−/g, '-');
        const result = evaluate(sanitized);
        setExpr(String(result));
        setValue(String(result));
      } catch (e: any) {
        setError('Invalid expression');
      }
      return;
    }
    if (k.value === 'neg') {
      setExpr(prev => prev ? (prev.endsWith(')') ? `(-1*${prev})` : (Number.isFinite(Number(prev)) ? String(-Number(prev)) : `(-${prev})`)) : '-');
      return;
    }
    if (k.value === 'factorial') {
      try {
        const num = Number(value);
        if (!Number.isInteger(num) || num < 0) throw new Error('Invalid');
        setExpr(prev => prev + String(factorial(num)));
      } catch {
        setError('Invalid factorial');
      }
      return;
    }
    if (k.value === 'ans') {
      setExpr(prev => prev + value);
      return;
    }

    setExpr(prev => prev + k.value);
  }, [expr, value]);

  // Keyboard support
  useEffect(() => {
    const onKeyDown = (e: KeyboardEvent) => {
      const target = e.target as HTMLElement | null;
      if (target && (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.isContentEditable)) return;
      const map: Record<string, string> = {
        '+': '+', '-': '-', '*': '*', '/': '/', '^': '^', '(': '(', ')': ')', '.': '.',
      };
      if (/[0-9]/.test(e.key)) setExpr(prev => prev + e.key);
      else if (e.key in map) setExpr(prev => prev + map[e.key]);
      else if (e.key === 'Enter' || e.key === '=') onKey({ label: '=', value: '=' });
      else if (e.key === 'Backspace') onKey({ label: 'C', value: 'C' });
      else if (e.key.toLowerCase() === 'c') onKey({ label: 'AC', value: 'AC' });
    };
    window.addEventListener('keydown', onKeyDown);
    return () => window.removeEventListener('keydown', onKeyDown);
  }, [onKey]);

  return (
    <div className={styles.wrapper} aria-label="Scientific calculator">
      <div className={styles.inner}>
        <div className={styles.display} role="status" aria-live="polite">
          <div className={styles.expr}>{error ? 'Error' : expr || '—'}</div>
          <div className={styles.value}>{value}</div>
        </div>
        <div className={styles.grid}>
          {keys.map((k) => (
            <button
              key={k.label}
              className={`${styles.btn} ${k.cls || ''}`}
              aria-label={k.aria || k.label}
              onClick={() => onKey(k)}
            >
              {k.label}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}

