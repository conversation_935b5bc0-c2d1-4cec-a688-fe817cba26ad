'use client';

import React, { useMemo, useState } from 'react';
import { <PERSON><PERSON>, <PERSON>c<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Calculator, FlaskConical, Sigma, RefreshCcw, LoaderCircle, Bot, Ruler } from 'lucide-react';
import { useVoiceInput } from '../hooks/useVoiceInput';
import { motion, AnimatePresence } from 'framer-motion';
import { Graph } from './Graph';

export type AIMode = 'nl-math' | 'unit-conversion' | 'formula-explainer' | 'graph';

interface AIConsoleProps {
  history: Array<{ expression: string; result: string }>;
  onInsert: (text: string) => void;
  defaultMode?: AIMode;
}

export const AIConsole: React.FC<AIConsoleProps> = ({ history, onInsert, defaultMode = 'nl-math' }) => {
  // Ensure SSR/CSR markup matches: start with defaultMode, then hydrate from localStorage
  const [mode, setMode] = useState<AIMode>(defaultMode);
  const [input, setInput] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [result, setResult] = useState<any>(null);
  const [expanded, setExpanded] = useState(true);
  const { status, transcript, start, stop } = useVoiceInput();

  // Hydrate mode from localStorage after mount to avoid hydration mismatch
  React.useEffect(() => {
    const saved = (localStorage.getItem('ai-mode') as AIMode) || defaultMode;
    if (saved !== mode) setMode(saved);
  }, [defaultMode]);

  React.useEffect(() => {
    localStorage.setItem('ai-mode', mode);
  }, [mode]);

  React.useEffect(() => {
    if (transcript) setInput(transcript);
  }, [transcript]);

  const icons = {
    'nl-math': Sparkles,
    'unit-conversion': Ruler,
    'formula-explainer': Sigma,
    'graph': FlaskConical,
  } as const;

  // Use a stable initial icon during SSR to avoid hydration mismatch
  const ModeIcon = icons[mode] || icons[defaultMode];

  const handleAsk = async () => {
    setError(null);
    setLoading(true);
    setResult(null);
    try {
      // Graph is handled client-side
      if (mode === 'graph') {
        setResult({ type: 'graph', expression: input });
        setLoading(false);
        return;
      }

      const res = await fetch('/api/ai', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ mode, prompt: input, history }),
      });

      // Try to parse JSON; if it fails, read as text for clearer error
      let data: any = null;
      const contentType = res.headers.get('content-type') || '';
      if (contentType.includes('application/json')) {
        data = await res.json().catch(() => null);
      } else {
        const text = await res.text().catch(() => '');
        if (!res.ok) throw new Error(text || 'AI request failed');
        try { data = JSON.parse(text); } catch { data = { error: text || 'Unexpected response' }; }
      }

      if (!res.ok) throw new Error(data?.error || 'AI request failed');
      if (data?.error) {
        setError(String(data.error));
        setResult(null);
      } else {
        setResult(data);
      }
    } catch (e: any) {
      setError(e?.message || 'Something went wrong');
    } finally {
      setLoading(false);
    }
  };

  const glass = 'bg-white/20 dark:bg-gray-900/30 backdrop-blur-xl border border-white/20 dark:border-white/10 shadow-[inset_0_1px_0_rgba(255,255,255,0.2)]';

  return (
    <div className={`rounded-2xl p-3 sm:p-4 ${glass}`}>
      {/* Compact header for mobile */}
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-2">
          <Bot size={18} className="text-gray-700 dark:text-gray-200" />
          <span className="text-sm font-medium text-gray-800 dark:text-gray-100">AI Assistant</span>
        </div>
        <button
          onClick={() => setExpanded(!expanded)}
          className="text-xs px-2 py-1 rounded-lg bg-white/40 dark:bg-gray-800/40"
        >
          {expanded ? 'Hide' : 'Show'}
        </button>
      </div>

      <AnimatePresence initial={false}>
        {(expanded || typeof window === 'undefined') && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            className="overflow-hidden"
          >
            {/* Mode selector */}
            <div className="flex items-center gap-2 mb-3">
              {(['nl-math', 'unit-conversion', 'formula-explainer', 'graph'] as AIMode[]).map(m => {
                const Icon = icons[m];
                const active = m === mode;
                return (
                  <button
                    key={m}
                    onClick={() => setMode(m)}
                    className={`flex items-center gap-2 px-2.5 py-1.5 rounded-xl transition-all text-xs ${
                      active
                        ? 'bg-gradient-to-r from-purple-500 to-blue-500 text-white shadow-lg shadow-purple-500/30'
                        : 'bg-white/40 dark:bg-gray-800/40 text-gray-700 dark:text-gray-200'
                    }`}
                  >
                    <Icon size={14} />
                    <span className="capitalize hidden xs:inline">{m.replace('-', ' ')}</span>
                  </button>
                );
              })}
            </div>

            {/* Input row */}
            <div className={`flex items-center gap-2 p-2 rounded-xl ${glass}`}>
              <ModeIcon size={18} className="text-gray-600 dark:text-gray-300" />
              <input
                value={input}
                onChange={(e) => setInput(e.target.value)}
                placeholder={mode === 'graph' ? 'y = 2x + 3' : 'Ask or convert'}
                className="flex-1 bg-transparent outline-none text-sm text-gray-900 dark:text-gray-100 placeholder:text-gray-400 dark:placeholder:text-gray-500"
              />
              {status !== 'listening' ? (
                <button onClick={start} className="p-2 rounded-lg hover:bg-white/40 dark:hover:bg-gray-800/40">
                  <Mic size={16} />
                </button>
              ) : (
                <button onClick={stop} className="p-2 rounded-lg hover:bg-white/40 dark:hover:bg-gray-800/40 text-red-500">
                  <MicOff size={16} />
                </button>
              )}
              <button
                onClick={handleAsk}
                disabled={!input || loading}
                className="px-3 py-2 rounded-xl bg-gradient-to-r from-blue-500 to-teal-500 text-white disabled:opacity-50 text-sm"
              >
                Ask
              </button>
            </div>

            {/* Results */}
            <div className="mt-3">
              <AnimatePresence>
                {loading && (
                  <motion.div
                    key="loading"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="w-full h-16 rounded-xl bg-gradient-to-r from-gray-200/40 via-gray-100/30 to-gray-200/40 dark:from-gray-700/30 dark:via-gray-800/30 dark:to-gray-700/30 animate-pulse"
                  />
                )}
              </AnimatePresence>

              <AnimatePresence>
                {error && (
                  <motion.div
                    key="error"
                    initial={{ y: 10, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    exit={{ y: 10, opacity: 0 }}
                    className="text-red-500 text-xs"
                  >
                    {error}
                  </motion.div>
                )}
              </AnimatePresence>

              {!loading && result && (
                <motion.div
                  initial={{ y: 10, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  className={`rounded-2xl p-3 mt-2 ${glass}`}
                >
                  {mode === 'nl-math' && (
                    <div className="flex items-center justify-between gap-3">
                      <div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">Result</div>
                        <div className="text-xl font-mono">{result.result}</div>
                        {result.explanation && (
                          <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">{result.explanation}</div>
                        )}
                      </div>
                      <button className="px-2.5 py-1.5 rounded-xl bg-blue-600 text-white text-xs" onClick={() => onInsert(String(result.result))}>
                        Insert
                      </button>
                    </div>
                  )}

                  {mode === 'unit-conversion' && (
                    <div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">Conversion</div>
                      {result.error ? (
                        <div className="text-red-500 text-xs">{result.error}</div>
                      ) : result.type === 'currency' ? (
                        <div className="space-y-1">
                          <div className="text-xl font-mono">{result.amount} {result.from} → {result.result} {result.to}</div>
                          {result.rate && <div className="text-[10px] text-gray-500">Rate: {result.rate}</div>}
                        </div>
                      ) : (
                        <div className="space-y-1">
                          <div className="text-xl font-mono">{result.from} → {result.to}</div>
                        </div>
                      )}
                    </div>
                  )}

                  {mode === 'formula-explainer' && (
                    <div>
                      <div className="text-xs text-gray-500 dark:text-gray-400 mb-2">Steps</div>
                      <ol className="list-decimal list-inside space-y-1 text-gray-900 dark:text-gray-100 text-sm">
                        {(result.steps || []).map((s: string, idx: number) => (
                          <li key={idx}>{s}</li>
                        ))}
                      </ol>
                      {result.final !== undefined && (
                        <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">Final: {String(result.final)}</div>
                      )}
                    </div>
                  )}

                  {mode === 'graph' && result?.expression && (
                    <div>
                      <Graph expression={result.expression} />
                    </div>
                  )}
                </motion.div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

