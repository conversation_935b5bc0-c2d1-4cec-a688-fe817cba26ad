.btn{
  /* Scoped CSS variables to this button to satisfy CSS Modules purity */
  --ai-start: #8b5cf6; /* purple-500 */
  --ai-end: #06b6d4;   /* cyan-500 */
  --ai-glow: rgba(99,102,241,0.6);

  position: relative;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  border: none;
  padding: 10px 14px;
  border-radius: 12px;
  color: #fff;
  background: linear-gradient(135deg, var(--ai-start), var(--ai-end));
  box-shadow: 0 8px 24px rgba(16,185,129,.25);
  cursor: pointer;
  transition: transform .12s ease, box-shadow .12s ease, filter .12s ease;
}
.btn:hover{ transform: translateY(-1px) scale(1.01); box-shadow: 0 10px 28px rgba(16,185,129,.35); }
.btn:active{ transform: translateY(0) scale(.99); }
.btn:disabled{ opacity: .6; cursor: not-allowed; }

.icon{
  width: 18px; height: 18px; border-radius: 9999px;
  box-shadow: 0 0 12px var(--ai-glow);
  animation: pulse 1.4s ease-in-out infinite;
  background: radial-gradient(circle, #fff 20%, rgba(255,255,255,.2) 60%, transparent 70%);
}
@keyframes pulse{ 0%,100%{ filter: drop-shadow(0 0 0 var(--ai-glow)); } 50%{ filter: drop-shadow(0 0 8px var(--ai-glow)); } }

.spinner{ width: 16px; height: 16px; border: 2px solid rgba(255,255,255,.4); border-top-color: #fff; border-radius: 9999px; animation: spin 1s linear infinite; }
@keyframes spin{ to{ transform: rotate(360deg); } }

.tooltip{
  position: absolute;
  top: calc(100% + 8px);
  left: 50%; transform: translateX(-50%);
  background: rgba(17,24,39,.95);
  color: #fff; padding: 8px 10px; border-radius: 8px; font-size: 12px; white-space: nowrap;
  box-shadow: 0 6px 18px rgba(0,0,0,.35);
}

