import OpenAI from 'openai';
import { NextResponse } from 'next/server';
import { unit as mathUnit, evaluate as mathEval } from 'mathjs';

const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

// AI provider selection (openai | groq)
const AI_PROVIDER = (process.env.AI_PROVIDER || 'openai').toLowerCase();

function canCallAI() {
  if (AI_PROVIDER === 'groq') return Boolean(process.env.GROQ_API_KEY);
  return Boolean(process.env.OPENAI_API_KEY);
}

type ChatMessage = { role: 'system' | 'user' | 'assistant'; content: string };

async function askModel(messages: ChatMessage[]): Promise<string> {
  if (AI_PROVIDER === 'groq') {
    const model = process.env.GROQ_MODEL || 'meta-llama/llama-3.1-8b-instruct';
    const res = await fetch('https://api.groq.com/openai/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.GROQ_API_KEY || ''}`,
      },
      body: JSON.stringify({ model, messages, temperature: 0.1 }),
    });
    if (!res.ok) {
      const errTxt = await res.text();
      throw new Error(`Groq error: ${res.status} ${errTxt}`);
    }
    const data = await res.json();
    return data.choices?.[0]?.message?.content ?? '';
  }

  // Default: OpenAI
  const response = await openai.responses.create({
    model: 'gpt-4o-mini',
    input: messages,
    temperature: 0.1,
  } as any);
  return (response as any).output_text ?? '';
}


// Types for request/response
interface AIRequestBody {
  mode: 'nl-math' | 'unit-conversion' | 'formula-explainer';
  prompt: string;
  expression?: string;
  history?: Array<{ expression: string; result: string }>; // last 5
}

export async function POST(req: Request) {
  try {
    const body = (await req.json()) as AIRequestBody;
    const { mode, prompt, expression, history = [] } = body;

    if (!mode || !prompt) {
      return NextResponse.json({ error: 'Missing mode or prompt' }, { status: 400 });
    }

    if (mode === 'unit-conversion') {
      const result = await handleUnitConversion(prompt);
      return NextResponse.json({ mode, ...result });
    }

    const contextLines = history
      .slice(0, 5)
      .map((h, i) => `${i + 1}. ${h.expression} = ${h.result}`)
      .join('\n');

    // If no API key or quota errors, fall back to local handling where possible
    const canCallOpenAI = Boolean(process.env.OPENAI_API_KEY);
    const canCallAnyAI = canCallAI();


    const systemBase = `You are a helpful math assistant for a calculator app. Prefer accurate numeric results. Avoid hallucinations and state when unsure.`;

    if (mode === 'nl-math') {
      // Try local evaluation first for plain math
      try {
        const sanitized = prompt.replace(/×/g, '*').replace(/÷/g, '/').replace(/−/g, '-');
        const result = mathEval(sanitized);
        if (typeof result === 'number' && isFinite(result)) {
          return NextResponse.json({ mode, result, explanation: 'Computed locally' });
        }
      } catch {}

      if (!canCallAnyAI) {
        return NextResponse.json({ mode, error: 'AI provider unavailable. Try a simpler numeric query.' }, { status: 200 });
      }

      const instructions = `Task: Solve the user's request as a calculation. Use order of operations.
- Output JSON with fields: result (number), explanation (short string). Only output JSON, no markdown.
- If ambiguous, state assumptions in explanation.
- Consider the recent history if it affects context.
History:\n${contextLines || 'None'}`;

      try {
        const text = await askModel([
          { role: 'system', content: systemBase },
          { role: 'user', content: `${instructions}\n\nQuestion: ${prompt}` },
        ]);
        const parsed = safeJson(text);
        return NextResponse.json({ mode, ...parsed });
      } catch (err: any) {
        // Graceful fallback on quota/error
        return NextResponse.json({ mode, error: 'AI provider error or quota exceeded. Please try later.' }, { status: 200 });
      }
    }

    if (mode === 'formula-explainer') {
      const target = (expression || prompt).replace(/×/g, '*').replace(/÷/g, '/').replace(/−/g, '-');

      // Try local step breakdown for simple arithmetic
      try {
        const steps: string[] = [];
        // Very simple step guide: split by + and - at top level after handling parentheses
        // This is minimal and only for basic expressions. Complex expressions still need AI.
        const final = mathEval(target);
        steps.push(`Compute: ${target}`);
        steps.push(`Result: ${final}`);
        return NextResponse.json({ mode, steps, final });
      } catch {}

      if (!canCallAnyAI) {
        return NextResponse.json({ mode, error: 'AI provider unavailable. Enter a simpler expression.' }, { status: 200 });
      }

      const instructions = `Task: Explain step-by-step how to compute the given expression.
- Output JSON with fields: steps (array of strings), final (number or string).
- Use clear and concise steps; avoid lengthy prose.
History:\n${contextLines || 'None'}`;

      try {
        const text = await askModel([
          { role: 'system', content: systemBase },
          { role: 'user', content: `${instructions}\n\nExpression: ${target}` },
        ]);
        const parsed = safeJson(text);
        return NextResponse.json({ mode, ...parsed });
      } catch (err: any) {
        return NextResponse.json({ mode, error: 'AI provider error or quota exceeded. Please try later.' }, { status: 200 });
      }
    }

    return NextResponse.json({ error: 'Unsupported mode' }, { status: 400 });
  } catch (error: any) {
    console.error('AI API error', error);
    return NextResponse.json({ error: error?.message || 'Server error' }, { status: 500 });
  }
}

function safeJson(text: string) {
  try {
    const start = text.indexOf('{');
    const end = text.lastIndexOf('}');
    if (start >= 0 && end >= 0) {
      const sliced = text.slice(start, end + 1);
      return JSON.parse(sliced);
    }
    return JSON.parse(text);
  } catch {
    return { raw: text };
  }
}

async function handleUnitConversion(query: string) {
  // Try to detect currency conversion pattern like: "100 USD to EUR" or "usd->eur 100"
  const currencyMatch = query.match(/(\d+(?:\.\d+)?)\s*([A-Za-z]{3})\s*(?:to|in|->)\s*([A-Za-z]{3})/i);
  if (currencyMatch) {
    const amount = parseFloat(currencyMatch[1]);
    const from = currencyMatch[2].toUpperCase();
    const to = currencyMatch[3].toUpperCase();

    const url = `https://api.exchangerate.host/convert?from=${from}&to=${to}&amount=${amount}`;
    const res = await fetch(url);
    if (!res.ok) throw new Error('Currency API failed');
    const data = await res.json();
    return {
      type: 'currency',
      query,
      amount,
      from,
      to,
      rate: data.info?.rate ?? null,
      result: data.result,
    };
  }

  // Length/weight using mathjs, e.g., "10 km to mi" or "5 kg in lb"
  const unitMatch = query.match(/(\d+(?:\.\d+)?)\s*([A-Za-z]+)\s*(?:to|in|->)\s*([A-Za-z]+)/i);
  if (unitMatch) {
    const value = parseFloat(unitMatch[1]);
    const fromUnit = unitMatch[2];
    const toUnit = unitMatch[3];
    try {
      const converted = mathUnit(`${value} ${fromUnit}`).to(toUnit);
      return {
        type: 'unit',
        query,
        from: `${value} ${fromUnit}`,
        to: converted.toString(),
        value: converted.value,
        unit: converted.units.map(u => u.unit.name).join('*'),
      };
    } catch (e: any) {
      return { type: 'unit', error: e?.message || 'Invalid unit conversion', query };
    }
  }

  return { type: 'unit', error: 'Could not parse conversion query', query };
}

