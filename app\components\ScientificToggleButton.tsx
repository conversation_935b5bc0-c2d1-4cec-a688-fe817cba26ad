'use client';

import React, { useState } from 'react';
import styles from './ScientificToggleButton.module.css';

interface Props {
  onToggle: () => Promise<string> | string | void;
  ariaLabel?: string;
  text?: string;
}

export default function ScientificToggleButton({ onToggle, ariaLabel = 'Open scientific calculator', text = 'Scientific' }: Props) {
  const [loading, setLoading] = useState(false);
  const [tooltip, setTooltip] = useState<string | null>(null);

  const onClick = async () => {
    if (loading) return;
    setLoading(true);
    try {
      // Simulate AI delay and get feedback text
      const delay = (ms: number) => new Promise(res => setTimeout(res, ms));
      await delay(700);
      const feedback = (await onToggle()) || 'Ready! Unlocked advanced functions.';
      setTooltip(String(feedback));
      await delay(1200);
      setTooltip(null);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ position: 'relative', display: 'inline-block' }}>
      <button
        className={styles.btn}
        onClick={onClick}
        disabled={loading}
        aria-label={ariaLabel}
      >
        <span className={styles.icon} aria-hidden="true" />
        <span>{text}</span>
        {loading && <span className={styles.spinner} role="status" aria-live="polite" />}
      </button>
      {tooltip && <div className={styles.tooltip} role="tooltip">{tooltip}</div>}
    </div>
  );
}

