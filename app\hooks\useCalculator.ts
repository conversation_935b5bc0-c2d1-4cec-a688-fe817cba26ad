'use client';

import { useState, useCallback, useEffect } from 'react';
import { evaluate, format } from 'mathjs';
import { CalculatorState, HistoryEntry, MemoryOperation } from '../types/calculator';

const initialState: CalculatorState = {
  display: '0',
  expression: '',
  result: '',
  history: [],
  memory: 0,
  isResultShown: false,
  hasError: false,
  errorMessage: '',
  lastOperation: '',
};

export const useCalculator = () => {
  const [state, setState] = useState<CalculatorState>(initialState);

  // Load history from localStorage on mount
  useEffect(() => {
    const savedHistory = localStorage.getItem('calculator-history');
    const savedMemory = localStorage.getItem('calculator-memory');
    
    if (savedHistory) {
      try {
        const history = JSON.parse(savedHistory).map((entry: any) => ({
          ...entry,
          timestamp: new Date(entry.timestamp),
        }));
        setState(prev => ({ ...prev, history }));
      } catch (error) {
        console.error('Failed to load history:', error);
      }
    }
    
    if (savedMemory) {
      const memory = parseFloat(savedMemory) || 0;
      setState(prev => ({ ...prev, memory }));
    }
  }, []);

  // Save history and memory to localStorage
  const saveToStorage = useCallback((history: HistoryEntry[], memory: number) => {
    localStorage.setItem('calculator-history', JSON.stringify(history));
    localStorage.setItem('calculator-memory', memory.toString());
  }, []);

  // Format number for display
  const formatNumber = useCallback((num: number): string => {
    if (isNaN(num) || !isFinite(num)) return 'Error';
    
    // Handle very large or very small numbers with scientific notation
    if (Math.abs(num) >= 1e15 || (Math.abs(num) < 1e-6 && num !== 0)) {
      return num.toExponential(6);
    }
    
    // Format with appropriate decimal places
    const formatted = format(num, { precision: 14 });
    return formatted;
  }, []);

  // Validate expression for safe evaluation
  const isValidExpression = useCallback((expr: string): boolean => {
    // Remove spaces and check for valid characters
    const cleaned = expr.replace(/\s/g, '');
    const validChars = /^[0-9+\-*/().%\s]*$/;
    
    if (!validChars.test(cleaned)) return false;
    
    // Check for balanced parentheses
    let parenCount = 0;
    for (const char of cleaned) {
      if (char === '(') parenCount++;
      if (char === ')') parenCount--;
      if (parenCount < 0) return false;
    }
    
    return parenCount === 0;
  }, []);

  // Evaluate expression safely
  const evaluateExpression = useCallback((expr: string): number => {
    try {
      if (!expr || expr === '0') return 0;
      
      // Replace display operators with mathjs operators
      const mathExpr = expr
        .replace(/×/g, '*')
        .replace(/÷/g, '/')
        .replace(/−/g, '-');
      
      if (!isValidExpression(mathExpr)) {
        throw new Error('Invalid expression');
      }
      
      const result = evaluate(mathExpr);
      
      if (typeof result !== 'number') {
        throw new Error('Invalid result type');
      }
      
      return result;
    } catch (error) {
      throw new Error('Calculation error');
    }
  }, [isValidExpression]);

  // Add entry to history
  const addToHistory = useCallback((expression: string, result: string) => {
    const entry: HistoryEntry = {
      id: Date.now().toString(),
      expression,
      result,
      timestamp: new Date(),
    };
    
    setState(prev => {
      const newHistory = [entry, ...prev.history].slice(0, 50); // Keep last 50 entries
      saveToStorage(newHistory, prev.memory);
      return { ...prev, history: newHistory };
    });
  }, [saveToStorage]);

  // Clear all
  const clear = useCallback(() => {
    setState(prev => ({
      ...prev,
      display: '0',
      expression: '',
      result: '',
      isResultShown: false,
      hasError: false,
      errorMessage: '',
      lastOperation: '',
    }));
  }, []);

  // Backspace
  const backspace = useCallback(() => {
    setState(prev => {
      if (prev.hasError || prev.isResultShown) {
        return {
          ...prev,
          display: '0',
          expression: '',
          result: '',
          isResultShown: false,
          hasError: false,
          errorMessage: '',
        };
      }
      
      const newDisplay = prev.display.length > 1 ? prev.display.slice(0, -1) : '0';
      const newExpression = prev.expression.length > 1 ? prev.expression.slice(0, -1) : '';
      
      return {
        ...prev,
        display: newDisplay,
        expression: newExpression,
      };
    });
  }, []);

  // Input number
  const inputNumber = useCallback((num: string) => {
    setState(prev => {
      if (prev.hasError) {
        return {
          ...prev,
          display: num,
          expression: num,
          hasError: false,
          errorMessage: '',
          isResultShown: false,
        };
      }
      
      if (prev.isResultShown) {
        return {
          ...prev,
          display: num,
          expression: num,
          result: '',
          isResultShown: false,
        };
      }
      
      const newDisplay = prev.display === '0' ? num : prev.display + num;
      const newExpression = prev.expression === '0' ? num : prev.expression + num;
      
      return {
        ...prev,
        display: newDisplay,
        expression: newExpression,
      };
    });
  }, []);

  // Input operator
  const inputOperator = useCallback((op: string) => {
    setState(prev => {
      if (prev.hasError) return prev;
      
      let newExpression = prev.expression;
      let newDisplay = prev.display;
      
      // Convert display operators
      const displayOp = op === '*' ? '×' : op === '/' ? '÷' : op === '-' ? '−' : op;
      
      if (prev.isResultShown && prev.result) {
        newExpression = prev.result + op;
        newDisplay = prev.result + displayOp;
      } else {
        // Replace last operator if the last character is an operator
        const lastChar = newExpression.slice(-1);
        if (['+', '-', '*', '/', '×', '÷', '−'].includes(lastChar)) {
          newExpression = newExpression.slice(0, -1) + op;
          newDisplay = newDisplay.slice(0, -1) + displayOp;
        } else {
          newExpression += op;
          newDisplay += displayOp;
        }
      }
      
      return {
        ...prev,
        display: newDisplay,
        expression: newExpression,
        isResultShown: false,
        lastOperation: op,
      };
    });
  }, []);

  // Input decimal point
  const inputDecimal = useCallback(() => {
    setState(prev => {
      if (prev.hasError || prev.isResultShown) {
        return {
          ...prev,
          display: '0.',
          expression: '0.',
          hasError: false,
          errorMessage: '',
          isResultShown: false,
        };
      }
      
      // Check if current number already has a decimal point
      const parts = prev.display.split(/[+\-×÷−]/);
      const currentNumber = parts[parts.length - 1];
      
      if (currentNumber.includes('.')) return prev;
      
      return {
        ...prev,
        display: prev.display + '.',
        expression: prev.expression + '.',
      };
    });
  }, []);

  // Calculate result
  const calculate = useCallback(() => {
    setState(prev => {
      if (prev.hasError || !prev.expression) return prev;
      
      try {
        const result = evaluateExpression(prev.expression);
        const formattedResult = formatNumber(result);
        
        addToHistory(prev.display, formattedResult);
        
        return {
          ...prev,
          result: formattedResult,
          display: formattedResult,
          isResultShown: true,
          hasError: false,
          errorMessage: '',
        };
      } catch (error) {
        return {
          ...prev,
          hasError: true,
          errorMessage: error instanceof Error ? error.message : 'Calculation error',
          display: 'Error',
        };
      }
    });
  }, [evaluateExpression, formatNumber, addToHistory]);

  // Memory operations
  const memoryOperation = useCallback((operation: MemoryOperation) => {
    setState(prev => {
      const currentValue = prev.isResultShown ? 
        parseFloat(prev.result) || 0 : 
        parseFloat(prev.display) || 0;
      
      let newMemory = prev.memory;
      
      switch (operation) {
        case 'MC':
          newMemory = 0;
          break;
        case 'MR':
          return {
            ...prev,
            display: formatNumber(prev.memory),
            expression: formatNumber(prev.memory),
            isResultShown: true,
          };
        case 'M+':
          newMemory = prev.memory + currentValue;
          break;
        case 'M-':
          newMemory = prev.memory - currentValue;
          break;
        case 'MS':
          newMemory = currentValue;
          break;
      }
      
      saveToStorage(prev.history, newMemory);
      
      return {
        ...prev,
        memory: newMemory,
      };
    });
  }, [formatNumber, saveToStorage]);

  // Toggle sign
  const toggleSign = useCallback(() => {
    setState(prev => {
      if (prev.hasError) return prev;
      
      const currentValue = parseFloat(prev.display) || 0;
      const newValue = -currentValue;
      const formatted = formatNumber(newValue);
      
      return {
        ...prev,
        display: formatted,
        expression: prev.isResultShown ? formatted : prev.expression.replace(/[^+\-×÷−]*$/, formatted),
        result: prev.isResultShown ? formatted : prev.result,
      };
    });
  }, [formatNumber]);

  // Calculate percentage
  const percentage = useCallback(() => {
    setState(prev => {
      if (prev.hasError) return prev;
      
      const currentValue = parseFloat(prev.display) || 0;
      const newValue = currentValue / 100;
      const formatted = formatNumber(newValue);
      
      return {
        ...prev,
        display: formatted,
        expression: prev.expression.replace(/[^+\-×÷−]*$/, formatted),
      };
    });
  }, [formatNumber]);

  // Clear history
  const clearHistory = useCallback(() => {
    setState(prev => ({ ...prev, history: [] }));
    localStorage.removeItem('calculator-history');
  }, []);

  // Live preview result as user types
  useEffect(() => {
    if (state.hasError || state.isResultShown) return;
    const expr = state.expression;
    if (!expr) {
      setState(prev => ({ ...prev, result: '' }));
      return;
    }
    // Avoid evaluating when ending on operator
    const last = expr.slice(-1);
    if (['+', '-', '*', '/', '×', '÷', '−', '('].includes(last)) {
      setState(prev => ({ ...prev, result: '' }));
      return;
    }
    try {
      const value = evaluateExpression(expr);
      const formatted = formatNumber(value);
      setState(prev => (prev.result === formatted ? prev : { ...prev, result: formatted }));
    } catch {
      // Silent: keep preview empty for incomplete input
      setState(prev => (prev.result ? { ...prev, result: '' } : prev));
    }
  }, [state.expression, state.hasError, state.isResultShown, evaluateExpression, formatNumber]);

  return {
    state,
    actions: {
      clear,
      backspace,
      inputNumber,
      inputOperator,
      inputDecimal,
      calculate,
      memoryOperation,
      toggleSign,
      percentage,
      clearHistory,
    },
  };
};
