'use client';

import { useCallback, useEffect, useRef, useState } from 'react';

type Status = 'idle' | 'listening' | 'error';

export function useVoiceInput() {
  const [status, setStatus] = useState<Status>('idle');
  const [transcript, setTranscript] = useState('');
  const recognitionRef = useRef<any>(null);

  useEffect(() => {
    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
    if (!SpeechRecognition) return;
    const rec = new SpeechRecognition();
    rec.lang = 'en-US';
    rec.continuous = false;
    rec.interimResults = true;

    rec.onstart = () => setStatus('listening');
    rec.onerror = () => setStatus('error');
    rec.onend = () => setStatus('idle');
    rec.onresult = (event: any) => {
      let temp = '';
      for (let i = event.resultIndex; i < event.results.length; ++i) {
        temp += event.results[i][0].transcript;
      }
      setTranscript(temp);
    };

    recognitionRef.current = rec;
    return () => rec.abort();
  }, []);

  const start = useCallback(() => {
    if (!recognitionRef.current) return;
    setTranscript('');
    recognitionRef.current.start();
  }, []);

  const stop = useCallback(() => {
    recognitionRef.current?.stop();
  }, []);

  return { status, transcript, start, stop };
}

