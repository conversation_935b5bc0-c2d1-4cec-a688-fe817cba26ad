'use client';

import React, { useState } from 'react';
import { Clock, X } from 'lucide-react';
import { CalculatorState, HistoryEntry } from '../types/calculator';

interface CalculatorDisplayProps {
  state: CalculatorState;
  onClearHistory: () => void;
}

export const CalculatorDisplay: React.FC<CalculatorDisplayProps> = ({
  state,
  onClearHistory,
}) => {
  const [showHistory, setShowHistory] = useState(false);

  const formatDisplayText = (text: string): string => {
    if (text.length > 12) {
      // For very long numbers, show in scientific notation or truncate
      const num = parseFloat(text);
      if (!isNaN(num) && isFinite(num)) {
        if (Math.abs(num) >= 1e12 || (Math.abs(num) < 1e-6 && num !== 0)) {
          return num.toExponential(6);
        }
      }
      return text.substring(0, 12) + '...';
    }
    return text;
  };

  const formatHistoryTime = (timestamp: Date): string => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    if (days < 7) return `${days}d ago`;
    return timestamp.toLocaleDateString();
  };

  return (
    <div className="glass rounded-2xl p-6 mb-6">
      {/* Expression Display */}
      <div className="mb-2">
        <div className="text-sm text-gray-600 dark:text-gray-300 min-h-[20px] text-right">
          {state.expression && !state.hasError ? state.expression : ''}
        </div>
      </div>

      {/* Main Display with premium gradient accent */}
      <div className="mb-4">
        <div
          className={`text-4xl md:text-5xl font-mono text-right min-h-[60px] flex items-center justify-end ${
            state.hasError
              ? 'text-red-400'
              : 'text-gray-900 dark:text-white'
          }`}
        >
          {state.hasError ? state.errorMessage : formatDisplayText(state.display)}
        </div>
      </div>

      {/* Result Preview */}
      {state.expression && !state.isResultShown && !state.hasError && (
        <div className="mb-4">
          <div className="text-lg text-gray-400 dark:text-gray-500 text-right">
            = {state.result || '...'}
          </div>
        </div>
      )}

      {/* Memory Indicator */}
      {state.memory !== 0 && (
        <div className="mb-4">
          <div className="text-sm text-blue-500 dark:text-blue-400 text-left">
            M: {state.memory}
          </div>
        </div>
      )}

      {/* History Toggle */}
      <div className="flex justify-between items-center">
        <button
          onClick={() => setShowHistory(!showHistory)}
          className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
        >
          <Clock size={16} />
          History ({state.history.length})
        </button>

        {state.history.length > 0 && (
          <button
            onClick={onClearHistory}
            className="text-sm text-red-400 hover:text-red-300 transition-colors"
          >
            Clear History
          </button>
        )}
      </div>

      {/* History Panel (full width, non-overlapping) */}
      {showHistory && (
        <div className="mt-4">
          <div className="glass rounded-xl p-4 shadow">
            <div className="max-h-72 overflow-y-auto">
              {state.history.length === 0 ? (
                <div className="text-center text-gray-500 dark:text-gray-400 py-8">
                  No calculations yet
                </div>
              ) : (
                <div className="space-y-3">
                  {state.history.map((entry) => (
                    <div
                      key={entry.id}
                      className="bg-white/40 dark:bg-gray-800/40 rounded-lg p-3 hover:bg-white/60 dark:hover:bg-gray-700/60 transition-colors cursor-pointer"
                      onClick={() => navigator.clipboard.writeText(entry.result)}
                    >
                      <div className="flex justify-between items-start gap-3">
                        <div className="flex-1 min-w-0">
                          <div className="text-sm text-gray-600 dark:text-gray-300 break-words">
                            {entry.expression}
                          </div>
                          <div className="text-lg font-mono text-gray-900 dark:text-white break-words">
                            = {entry.result}
                          </div>
                        </div>
                        <div className="text-xs text-gray-400 dark:text-gray-500 ml-2 flex-shrink-0 whitespace-nowrap">
                          {formatHistoryTime(entry.timestamp)}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
