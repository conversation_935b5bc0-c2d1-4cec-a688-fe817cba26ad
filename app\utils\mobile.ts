'use client';

// Mobile utility functions for enhanced mobile experience

export interface MobileFeatures {
  vibrate: (pattern?: number | number[]) => void;
  isStandalone: () => boolean;
  isMobile: () => boolean;
  isIOS: () => boolean;
  isAndroid: () => boolean;
  canInstall: () => boolean;
  requestInstall: () => Promise<void>;
}

class MobileUtils implements MobileFeatures {
  private deferredPrompt: any = null;

  constructor() {
    if (typeof window !== 'undefined') {
      // Listen for the beforeinstallprompt event
      window.addEventListener('beforeinstallprompt', (e) => {
        e.preventDefault();
        this.deferredPrompt = e;
      });
    }
  }

  /**
   * Trigger haptic feedback on supported devices
   */
  vibrate(pattern: number | number[] = 50): void {
    if ('vibrate' in navigator) {
      navigator.vibrate(pattern);
    }
  }

  /**
   * Check if the app is running in standalone mode (installed as PWA)
   */
  isStandalone(): boolean {
    if (typeof window === 'undefined') return false;
    
    return (
      window.matchMedia('(display-mode: standalone)').matches ||
      (window.navigator as any).standalone === true ||
      document.referrer.includes('android-app://')
    );
  }

  /**
   * Detect if the user is on a mobile device
   */
  isMobile(): boolean {
    if (typeof window === 'undefined') return false;
    
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent
    );
  }

  /**
   * Detect if the user is on iOS
   */
  isIOS(): boolean {
    if (typeof window === 'undefined') return false;
    
    return /iPad|iPhone|iPod/.test(navigator.userAgent);
  }

  /**
   * Detect if the user is on Android
   */
  isAndroid(): boolean {
    if (typeof window === 'undefined') return false;
    
    return /Android/.test(navigator.userAgent);
  }

  /**
   * Check if the app can be installed
   */
  canInstall(): boolean {
    return this.deferredPrompt !== null;
  }

  /**
   * Request app installation
   */
  async requestInstall(): Promise<void> {
    if (!this.deferredPrompt) {
      throw new Error('App cannot be installed');
    }

    const result = await this.deferredPrompt.prompt();
    console.log('Install prompt result:', result);
    
    this.deferredPrompt = null;
  }

  /**
   * Get device orientation
   */
  getOrientation(): 'portrait' | 'landscape' {
    if (typeof window === 'undefined') return 'portrait';
    
    return window.innerHeight > window.innerWidth ? 'portrait' : 'landscape';
  }

  /**
   * Lock orientation (if supported)
   */
  async lockOrientation(orientation: 'portrait' | 'landscape'): Promise<void> {
    if ('screen' in window && 'orientation' in window.screen) {
      try {
        await (window.screen.orientation as any).lock(orientation);
      } catch (error) {
        console.warn('Orientation lock not supported:', error);
      }
    }
  }

  /**
   * Prevent zoom on double tap (for calculator buttons)
   */
  preventZoom(): void {
    if (typeof document === 'undefined') return;
    
    let lastTouchEnd = 0;
    document.addEventListener('touchend', (event) => {
      const now = new Date().getTime();
      if (now - lastTouchEnd <= 300) {
        event.preventDefault();
      }
      lastTouchEnd = now;
    }, false);
  }

  /**
   * Add touch-action CSS to prevent unwanted gestures
   */
  optimizeTouch(): void {
    if (typeof document === 'undefined') return;
    
    const style = document.createElement('style');
    style.textContent = `
      .calculator-button {
        touch-action: manipulation;
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        user-select: none;
      }
      
      .calculator-display {
        touch-action: pan-y;
        -webkit-overflow-scrolling: touch;
      }
    `;
    document.head.appendChild(style);
  }

  /**
   * Setup viewport for mobile optimization
   */
  setupViewport(): void {
    if (typeof document === 'undefined') return;
    
    let viewport = document.querySelector('meta[name="viewport"]');
    if (!viewport) {
      viewport = document.createElement('meta');
      viewport.setAttribute('name', 'viewport');
      document.head.appendChild(viewport);
    }
    
    viewport.setAttribute(
      'content',
      'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover'
    );
  }

  /**
   * Add safe area insets for devices with notches
   */
  addSafeAreaSupport(): void {
    if (typeof document === 'undefined') return;
    
    const style = document.createElement('style');
    style.textContent = `
      .safe-area-top {
        padding-top: env(safe-area-inset-top);
      }
      
      .safe-area-bottom {
        padding-bottom: env(safe-area-inset-bottom);
      }
      
      .safe-area-left {
        padding-left: env(safe-area-inset-left);
      }
      
      .safe-area-right {
        padding-right: env(safe-area-inset-right);
      }
    `;
    document.head.appendChild(style);
  }
}

// Export singleton instance
export const mobileUtils = new MobileUtils();

// Hook for React components
export const useMobile = () => {
  return mobileUtils;
};
