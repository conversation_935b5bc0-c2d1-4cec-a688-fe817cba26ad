export type OperatorType = '+' | '-' | '*' | '/' | '%' | '=' | 'clear' | 'backspace' | 'decimal';

export type FunctionType = 'sin' | 'cos' | 'tan' | 'log' | 'ln' | 'sqrt' | 'square' | 'inverse';

export type MemoryOperation = 'MC' | 'MR' | 'M+' | 'M-' | 'MS';

export interface CalculatorState {
  display: string;
  expression: string;
  result: string;
  history: HistoryEntry[];
  memory: number;
  isResultShown: boolean;
  hasError: boolean;
  errorMessage: string;
  lastOperation: string;
}

export interface HistoryEntry {
  id: string;
  expression: string;
  result: string;
  timestamp: Date;
}

export interface ButtonConfig {
  label: string;
  value: string;
  type: 'number' | 'operator' | 'function' | 'memory' | 'special';
  className?: string;
  colspan?: number;
  rowspan?: number;
}

export interface ThemeConfig {
  isDark: boolean;
  colors: {
    primary: string;
    secondary: string;
    background: string;
    surface: string;
    text: string;
    textSecondary: string;
    accent: string;
    error: string;
  };
}

export interface CalculatorSettings {
  theme: 'light' | 'dark' | 'system';
  vibrationEnabled: boolean;
  soundEnabled: boolean;
  precision: number;
  angleUnit: 'deg' | 'rad';
  showHistory: boolean;
}

export interface CalculatorAction {
  type: 'INPUT' | 'OPERATOR' | 'FUNCTION' | 'MEMORY' | 'CLEAR' | 'BACKSPACE' | 'EQUALS' | 'TOGGLE_SIGN' | 'PERCENTAGE';
  payload?: string | number;
}
