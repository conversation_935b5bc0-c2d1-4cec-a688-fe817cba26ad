@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #0f172a; /* slate-900 */
  --accent: linear-gradient(135deg, #8b5cf6 0%, #06b6d4 100%); /* purple->cyan */
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0b1220; /* deep slate */
    --foreground: #e2e8f0; /* slate-200 */
  }
}

html, body {
  min-height: 100%;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans), ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Ubuntu, Cantarell, Noto Sans, Helvetica Neue, Arial, "Apple Color Emoji", "Segoe UI Emoji";
}

/* Glass + Neu helpers */
.glass {
  @apply bg-white/20 dark:bg-gray-900/30 backdrop-blur-xl border border-white/20 dark:border-white/10 shadow-[inset_0_1px_0_rgba(255,255,255,0.2)];
}
.neu {
  box-shadow: 8px 8px 24px rgba(0,0,0,0.18), -8px -8px 24px rgba(255,255,255,0.12);
}

/* Premium gradient text */
.gradient-text {
  background: var(--accent);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

/* Shimmer loading */
.shimmer {
  background: linear-gradient(90deg, rgba(0,0,0,0.06) 25%, rgba(0,0,0,0.03) 37%, rgba(0,0,0,0.06) 63%);
  background-size: 400% 100%;
  animation: shimmer 1.4s ease infinite;
}
@keyframes shimmer {
  0% { background-position: 100% 0; }
  100% { background-position: 0 0; }
}
