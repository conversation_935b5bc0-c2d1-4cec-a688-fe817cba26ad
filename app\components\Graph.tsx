'use client';

import React, { useMemo } from 'react';
import { evaluate } from 'mathjs';

interface GraphProps {
  expression: string; // e.g., y=2x+3 or 2x+3
  width?: number;
  height?: number;
  xRange?: [number, number];
}

export const Graph: React.FC<GraphProps> = ({
  expression,
  width = 600,
  height = 300,
  xRange = [-10, 10],
}) => {
  // Normalize expression
  const rhs = useMemo(() => {
    const trimmed = expression.replace(/\s+/g, '');
    const match = trimmed.match(/y=([^=]+)/i);
    return match ? match[1] : trimmed;
  }, [expression]);

  const points = useMemo(() => {
    const [xMin, xMax] = xRange;
    const samples = 200;
    const dx = (xMax - xMin) / samples;
    const coords: Array<[number, number]> = [];
    for (let i = 0; i <= samples; i++) {
      const x = xMin + i * dx;
      try {
        const y = evaluate(rhs.replace(/\^/g, '**'), { x });
        if (typeof y === 'number' && isFinite(y)) {
          coords.push([x, y]);
        }
      } catch {}
    }
    return coords;
  }, [rhs, xRange]);

  // Determine y range from points
  const yRange = useMemo(() => {
    if (points.length === 0) return [-10, 10] as [number, number];
    let yMin = Infinity;
    let yMax = -Infinity;
    for (const [, y] of points) {
      yMin = Math.min(yMin, y);
      yMax = Math.max(yMax, y);
    }
    if (!isFinite(yMin) || !isFinite(yMax) || yMin === yMax) return [-10, 10];
    return [yMin, yMax] as [number, number];
  }, [points]);

  const [xMin, xMax] = xRange;
  const [yMin, yMax] = yRange;

  // Map function
  const mapX = (x: number) => ((x - xMin) / (xMax - xMin)) * width;
  const mapY = (y: number) => height - ((y - yMin) / (yMax - yMin)) * height;

  const pathD = useMemo(() => {
    if (points.length === 0) return '';
    return points
      .map(([x, y], idx) => `${idx === 0 ? 'M' : 'L'} ${mapX(x)} ${mapY(y)}`)
      .join(' ');
  }, [points]);

  // Axes
  const x0 = mapY(0); // y=0
  const y0 = mapX(0); // x=0

  return (
    <svg width="100%" height={height} viewBox={`0 0 ${width} ${height}`} className="rounded-xl bg-gradient-to-br from-gray-50/50 to-gray-100/30 dark:from-gray-800/40 dark:to-gray-900/20 backdrop-blur-md">
      {/* grid */}
      <defs>
        <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
          <path d="M 20 0 L 0 0 0 20" fill="none" stroke="currentColor" opacity="0.08" strokeWidth="1" />
        </pattern>
      </defs>
      <rect width="100%" height="100%" fill="url(#grid)" />

      {/* axes */}
      <line x1={0} y1={x0} x2={width} y2={x0} stroke="currentColor" opacity="0.2" />
      <line x1={y0} y1={0} x2={y0} y2={height} stroke="currentColor" opacity="0.2" />

      {/* curve */}
      <path d={pathD} fill="none" stroke="#60A5FA" strokeWidth={2} />
    </svg>
  );
};

