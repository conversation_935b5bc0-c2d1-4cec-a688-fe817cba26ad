# AI Calculator - Modern Calculator Web App

A fully functional, responsive calculator web app built with Next.js 15, TypeScript, and Tailwind CSS. Features a modern design with dark/light mode toggle, PWA support, and mobile optimization.

![Calculator Screenshot](./public/screenshots/desktop.png)

## ✨ Features

### Core Functionality
- ✅ Basic arithmetic operations (+, −, ×, ÷)
- ✅ Percentage calculations
- ✅ Decimal point support
- ✅ Parentheses and order of operations (BODMAS)
- ✅ Real-time result preview
- ✅ Backspace/delete functionality
- ✅ Clear and reset operations

### Advanced Features
- ✅ Memory functions (M+, M-, MR, MC, MS)
- ✅ Toggle sign (±) functionality
- ✅ Calculation history with timestamps
- ✅ Large number handling with scientific notation
- ✅ Input validation and error handling

### UI/UX
- ✅ Clean, modern, mobile-first design
- ✅ Dark and light mode toggle with system preference detection
- ✅ Touch-friendly buttons with haptic feedback
- ✅ Responsive grid layout
- ✅ Animated button press effects
- ✅ Keyboard support for desktop users

### PWA & Mobile Features
- ✅ Progressive Web App (PWA) support
- ✅ Offline functionality with service worker
- ✅ Installable on mobile devices
- ✅ Vibration feedback on button press
- ✅ Safe area support for devices with notches
- ✅ Touch optimizations and zoom prevention

### Technical Features
- ✅ Built with Next.js 15 and TypeScript
- ✅ Styled with Tailwind CSS
- ✅ Safe math evaluation using mathjs library
- ✅ Local storage for history and memory persistence
- ✅ SEO-friendly with proper meta tags
- ✅ Capacitor-ready for mobile app deployment

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm, yarn, or pnpm

### Installation

1. Clone the repository:
```bash
git clone https://github.com/your-username/ai-calculator.git
cd ai-calculator
```

2. Install dependencies:
```bash
npm install
# or
yarn install
# or
pnpm install
```

3. Run the development server:
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

### Building for Production

```bash
npm run build
npm run start
```

## 📱 Mobile App Deployment with Capacitor

### Prerequisites for Mobile Development

1. Install Capacitor CLI:
```bash
npm install -g @capacitor/cli
```

2. For iOS development:
   - macOS with Xcode 12+
   - iOS Simulator or physical iOS device
   - Apple Developer Account (for App Store deployment)

3. For Android development:
   - Android Studio with SDK
   - Java Development Kit (JDK) 11+
   - Android device or emulator

### Setting Up Capacitor

1. Initialize Capacitor in your project:
```bash
npx cap init ai-calculator com.yourcompany.aicalculator
```

2. Build the web app:
```bash
npm run build
```

3. Add mobile platforms:
```bash
npx cap add ios
npx cap add android
```

4. Copy web assets to native projects:
```bash
npx cap copy
```

### iOS Development

1. Open the iOS project in Xcode:
```bash
npx cap open ios
```

2. Configure your app in Xcode:
   - Set your development team
   - Configure bundle identifier
   - Set deployment target (iOS 13+)

3. Build and run:
   - Select your target device/simulator
   - Click the play button in Xcode

### Android Development

1. Open the Android project in Android Studio:
```bash
npx cap open android
```

2. Configure your app:
   - Update `android/app/src/main/AndroidManifest.xml`
   - Set minimum SDK version (API 22+)
   - Configure app permissions

3. Build and run:
   - Select your target device/emulator
   - Click the run button in Android Studio

### Publishing to App Stores

#### Google Play Store

1. Generate a signed APK:
   - In Android Studio: Build → Generate Signed Bundle/APK
   - Choose Android App Bundle (recommended)
   - Create or use existing keystore

2. Upload to Google Play Console:
   - Create app listing
   - Upload AAB file
   - Complete store listing information
   - Submit for review

#### Apple App Store

1. Archive the app in Xcode:
   - Product → Archive
   - Distribute App → App Store Connect

2. Upload to App Store Connect:
   - Complete app information
   - Add screenshots and metadata
   - Submit for review

## 🛠️ Development

### Project Structure

```
ai-calculator/
├── app/
│   ├── components/          # React components
│   │   ├── Calculator.tsx   # Main calculator component
│   │   ├── CalculatorButton.tsx
│   │   └── CalculatorDisplay.tsx
│   ├── contexts/           # React contexts
│   │   └── ThemeContext.tsx
│   ├── hooks/              # Custom React hooks
│   │   └── useCalculator.ts
│   ├── types/              # TypeScript type definitions
│   │   └── calculator.ts
│   ├── utils/              # Utility functions
│   │   └── mobile.ts
│   ├── globals.css         # Global styles
│   ├── layout.tsx          # Root layout
│   └── page.tsx            # Home page
├── public/
│   ├── icons/              # PWA icons
│   ├── manifest.json       # PWA manifest
│   └── sw.js              # Service worker
└── README.md
```

### Key Technologies

- **Next.js 15**: React framework with App Router
- **TypeScript**: Type-safe JavaScript
- **Tailwind CSS**: Utility-first CSS framework
- **mathjs**: Safe mathematical expression evaluator
- **Lucide React**: Icon library
- **Capacitor**: Native mobile app wrapper

### Customization

#### Themes
Modify theme colors in `app/contexts/ThemeContext.tsx` and Tailwind configuration.

#### Calculator Logic
Extend calculator functionality in `app/hooks/useCalculator.ts`.

#### Mobile Features
Add more mobile-specific features in `app/utils/mobile.ts`.

## 🧪 Testing

Run the development server and test:

1. **Basic Operations**: Test all arithmetic operations
2. **Memory Functions**: Test M+, M-, MR, MC operations
3. **Edge Cases**: Test division by zero, large numbers
4. **Mobile Features**: Test on mobile devices
5. **PWA Features**: Test offline functionality and installation

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📞 Support

If you have any questions or need help with deployment, please open an issue on GitHub.

---

Built with ❤️ using Next.js, TypeScript, and Tailwind CSS.
