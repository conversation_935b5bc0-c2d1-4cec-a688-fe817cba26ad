'use client';

import React, { useCallback, useEffect } from 'react';
import { Moon, Sun, Monitor } from 'lucide-react';
import { useCalculator } from '../hooks/useCalculator';
import { useTheme } from '../contexts/ThemeContext';
import { CalculatorDisplay } from './CalculatorDisplay';
import { CalculatorButton } from './CalculatorButton';
import { ButtonConfig } from '../types/calculator';
// import { motion } from 'framer-motion';
import { AIConsole } from './AIConsole';


export const Calculator: React.FC = () => {
  const { state, actions } = useCalculator();
  const { theme, toggleTheme } = useTheme();

  // Button configuration
  const buttonConfig: ButtonConfig[] = [
    // Row 1: Memory and Clear functions
    { label: 'MC', value: 'MC', type: 'memory' },
    { label: 'MR', value: 'MR', type: 'memory' },
    { label: 'M+', value: 'M+', type: 'memory' },
    { label: 'M-', value: 'M-', type: 'memory' },

    // Row 2: Clear and operations
    { label: 'C', value: 'clear', type: 'special' },
    { label: '⌫', value: 'backspace', type: 'special' },
    { label: '(', value: '(', type: 'operator' },
    { label: ')', value: ')', type: 'operator' },

    // Row 3: Conversions and divide
    { label: '%', value: 'percentage', type: 'function' },
    { label: 'π', value: 'pi', type: 'function' },
    { label: 'ANS', value: 'ans', type: 'function' },
    { label: '÷', value: '/', type: 'operator' },

    // Row 4: Numbers and operations
    { label: '7', value: '7', type: 'number' },
    { label: '8', value: '8', type: 'number' },
    { label: '9', value: '9', type: 'number' },
    { label: '×', value: '*', type: 'operator' },
    
    // Row 4: Numbers and operations
    { label: '4', value: '4', type: 'number' },
    { label: '5', value: '5', type: 'number' },
    { label: '6', value: '6', type: 'number' },
    { label: '−', value: '-', type: 'operator' },
    
    // Row 5: Numbers and operations
    { label: '1', value: '1', type: 'number' },
    { label: '2', value: '2', type: 'number' },
    { label: '3', value: '3', type: 'number' },
    { label: '+', value: '+', type: 'operator' },
    
    // Row 6: Zero, decimal, and equals
    { label: '±', value: 'toggle-sign', type: 'function' },
    { label: '0', value: '0', type: 'number' },
    { label: '.', value: 'decimal', type: 'function' },
    { label: '=', value: '=', type: 'operator' },
  ];

  // Handle button clicks
  const handleButtonClick = useCallback((value: string, type: ButtonConfig['type']) => {
    switch (type) {
      case 'number':
        actions.inputNumber(value);
        break;
      case 'operator':
        if (value === '=') {
          actions.calculate();
        } else {
          actions.inputOperator(value);
        }
        break;
      case 'function':
        switch (value) {
          case 'decimal':
            actions.inputDecimal();
            break;
          case 'percentage':
            actions.percentage();
            break;
          case 'toggle-sign':
            actions.toggleSign();
            break;
          case 'ans': {
            // Insert last numeric result (absolute digits then toggle sign if needed)
            const ansStr = state.result || state.display || '0';
            if (!/^-?[0-9]*\.?[0-9]+$/.test(ansStr)) {
              break; // ignore non-numeric
            }
            const isNeg = ansStr.trim().startsWith('-');
            const abs = isNeg ? ansStr.trim().slice(1) : ansStr.trim();
            if (state.isResultShown) actions.clear();
            for (const ch of abs) {
              if (ch === '.') actions.inputDecimal();
              else actions.inputNumber(ch);
            }
            if (isNeg) actions.toggleSign();
            break;
          }
          case 'pi': {
            const pi = Math.PI.toString();
            // If result is currently shown, start a new entry with pi
            if (state.isResultShown || state.display === '0') {
              actions.clear();
            }
            for (const ch of pi) {
              if (ch === '.') actions.inputDecimal();
              else actions.inputNumber(ch);
            }
            break;
          }
        }
        break;
      case 'memory':
        actions.memoryOperation(value as any);
        break;
      case 'special':
        switch (value) {
          case 'clear':
            actions.clear();
            break;
          case 'backspace':
            actions.backspace();
            break;
        }
        break;
    }
  }, [actions]);

  // Keyboard support
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      const target = event.target as HTMLElement | null;
      if (target) {
        const tag = target.tagName;
        const isEditable = (target as HTMLElement).isContentEditable;
        if (tag === 'INPUT' || tag === 'TEXTAREA' || isEditable) {
          // Let form fields handle typing normally
          return;
        }
      }

      const key = event.key;
      let handled = false;

      // Numbers
      if (/^[0-9]$/.test(key)) {
        actions.inputNumber(key);
        handled = true;
      } else {
        // Operators and controls
        switch (key) {
          case '+':
            actions.inputOperator('+');
            handled = true;
            break;
          case '-':
            actions.inputOperator('-');
            handled = true;
            break;
          case '*':
            actions.inputOperator('*');
            handled = true;
            break;
          case '/':
            actions.inputOperator('/');
            handled = true;
            break;
          case '=':
          case 'Enter':
            actions.calculate();
            handled = true;
            break;
          case '.':
            actions.inputDecimal();
            handled = true;
            break;
          case '%':
            actions.percentage();
            handled = true;
            break;
          case 'Backspace':
            actions.backspace();
            handled = true;
            break;
          case 'Delete':
          case 'Escape':
            actions.clear();
            handled = true;
            break;
        }
      }

      if (handled) {
        event.preventDefault();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [actions]);

  const getThemeIcon = () => {
    switch (theme) {
      case 'light':
        return <Sun size={20} />;
      case 'dark':
        return <Moon size={20} />;
      default:
        return <Monitor size={20} />;
    }
  };


  return (
    <div className="max-w-4xl mx-auto p-4">
      {/* Header with theme toggle */}
      <div className="flex items-center justify-between mb-4 md:mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Calculator</h1>
        <div className="flex items-center gap-2">

          <button
            onClick={toggleTheme}
            className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
            aria-label="Toggle theme"
          >
            {getThemeIcon()}
          </button>
        </div>
      </div>

      {/* Display */}
      <CalculatorDisplay
        state={state}
        onClearHistory={actions.clearHistory}
      />

      {/* AI Console above the number pad on all devices */}
      <div className="mb-4">
        <AIConsole
          history={state.history.map(h => ({ expression: h.expression, result: h.result }))}
          onInsert={(text) => {
            actions.clear();
            for (const ch of text) {
              if (/\d/.test(ch)) actions.inputNumber(ch);
              else if (/[+\-*/]/.test(ch)) actions.inputOperator(ch);
              else if (ch === '.') actions.inputDecimal();
            }
          }}
          defaultMode="nl-math"
        />
      </div>


      {/* Button Grid with simple styles as requested */}
      <div className="grid grid-cols-4 gap-4 p-4">
        {buttonConfig.map((config, index) => (
          <CalculatorButton
            key={`${config.value}-${index}`}
            config={config}
            onClick={handleButtonClick}
          />
        ))}
      </div>

      {/* Footer */}
      <div className="mt-6 text-center text-sm text-gray-500 dark:text-gray-400">
        <p>Use keyboard for quick input</p>
        <p className="mt-1">Tap history entries to copy results</p>
      </div>
    </div>
  );
};
