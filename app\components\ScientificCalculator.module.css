.wrapper{
  /* Scope CSS variables within the wrapper to satisfy CSS Modules purity */
  --sc-bg: linear-gradient(135deg, #0ea5e9, #8b5cf6);
  --sc-surface: rgba(255,255,255,0.12);
  --sc-text: #fff;
  --sc-accent: #f97316; /* orange-500 */

  border-radius: 16px;
  background: var(--sc-bg);
  padding: 1px;
}

.inner{
  background: rgba(17,24,39,0.9); /* gray-900 */
  border-radius: 16px;
  padding: 12px;
}

.display{
  color: var(--sc-text);
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  background: var(--sc-surface);
  border-radius: 12px;
  padding: 10px 12px;
  min-height: 56px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.expr{ opacity: 0.8; font-size: 12px; word-break: break-all; }
.value{ font-size: 22px; font-weight: 700; word-break: break-all; }

.grid{ display: grid; grid-template-columns: repeat(5, minmax(0,1fr)); gap: 8px; margin-top: 12px; }
.btn{
  background: rgba(255,255,255,0.08);
  color: var(--sc-text);
  border-radius: 10px;
  padding: 10px 8px;
  font-weight: 600;
  transition: transform .12s ease, box-shadow .12s ease, background .12s ease;
  box-shadow: 0 2px 0 rgba(255,255,255,0.12) inset, 0 2px 8px rgba(0,0,0,0.25);
}
.btn:hover{ background: rgba(255,255,255,0.12); transform: translateY(-1px); }
.btn:active{ transform: translateY(0); box-shadow: 0 1px 0 rgba(255,255,255,0.08) inset; }
.btn:disabled{ opacity: .5; }

.btnAccent{ background: var(--sc-accent); }
.btnDanger{ background: #ef4444; }
.btnOp{ background: rgba(59,130,246,0.7); }

.rowSpan2{ grid-row: span 2; }
.colSpan2{ grid-column: span 2; }

/* Responsive */
@media (max-width: 640px){
  .value{ font-size: 18px; }
  .grid{ gap: 6px; }
  .btn{ padding: 10px 6px; font-size: 14px; }
}

